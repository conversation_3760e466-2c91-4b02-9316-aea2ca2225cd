import os
from playwright.sync_api import sync_playwright
import time
import datetime

from dotenv import load_dotenv
load_dotenv()
# 從環境變數獲取帳號和密碼
username = os.getenv('OPENERP_USERNAME')
password = os.getenv('OPENERP_PASSWORD')

# 如果環境變數未設置，則使用預設值
if username is None:
    username = "your_username"
if password is None:
    password = "your_password"

# 定義打卡日期（預設為當天）
def get_today_date():
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    return today

today = get_today_date()

def main():
    browser = None
    try:
        with sync_playwright() as p:
            # 啟動瀏覽器 (使用 headless 模式避免衝突)
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()

            # 訪問登入頁面
            page.goto("https://openerp.entrust.com.tw/pcpunch/Login.aspx")

            # 等待頁面加載完成
            page.wait_for_timeout(3000)  # 等待3秒讓頁面加載

            # 填寫帳號和密碼
            # 增加等待時間確保元素可用
            page.wait_for_selector("input[name='txt_EmpNo']", timeout=60000)
            page.fill("input[name='txt_EmpNo']", username)
            page.wait_for_selector("input[name='txt_Password']", timeout=60000)
            page.fill("input[name='txt_Password']", password)

            # 點擊登入按鈕
            page.wait_for_selector("input[id='btnLogin']", timeout=60000)
            page.click("input[id='btnLogin']")

            # 等待頁面加載
            # 等待頁面加載完成（替換為正確的 wait_for_load_state 方法）
            page.wait_for_load_state('networkidle', timeout=60000)

            # 檢查是否登入成功
            try:
                # 等待打卡相關元素出現，如果60秒內出現則表示登入成功
                page.wait_for_selector("text=打卡", timeout=60000)
                print("登入成功！")

                # 導航到打卡記錄頁面
                page.goto("https://openerp.entrust.com.tw/pcpunch/PunchRecord.aspx", timeout=60000)

                # 檢查當天是否有打卡記錄
                try:
                    page.wait_for_selector("div.punch-record", timeout=10000)
                    print(f"今天 {today} 有打卡記錄！")
                    return True  # 成功完成
                except:
                    print(f"今天 {today} 沒有打卡記錄。")
                    return True  # 成功完成（沒有打卡記錄也算成功）
            except:
                print("登入失敗，將重試...")
                return False  # 登入失敗

    except Exception as e:
        print(f"執行過程中發生錯誤: {e}")
        return False

# 執行主函數，最多重試5次
success = False
for attempt in range(5):
    print(f"嘗試 {attempt+1}/5...")
    success = main()
    if success:
        print("打卡檢查完成！")
        break
    else:
        if attempt < 4:  # 不是最後一次嘗試
            print("等待5秒後重試...")
            time.sleep(5)  # 等待5秒後重試

if not success:
    print("所有嘗試都失敗了。")