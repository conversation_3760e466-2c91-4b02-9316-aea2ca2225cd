import os
from playwright.sync_api import sync_playwright
import time
import datetime

from dotenv import load_dotenv
load_dotenv()
# 從環境變數獲取帳號和密碼
username = os.getenv('OPENERP_USERNAME')
password = os.getenv('OPENERP_PASSWORD')

# 如果環境變數未設置，則使用預設值
if username is None:
    username = "your_username"
if password is None:
    password = "your_password"

# 定義打卡日期（預設為當天）
def get_today_date():
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    return today

today = get_today_date()

def check_punch_records(page, target_date, punch_time):
    """檢查打卡記錄並驗證是否在預期時間範圍內"""
    try:
        # 查找表格中的所有行
        rows = page.query_selector_all("table tr")

        today_records = []

        for row in rows:
            try:
                cells = row.query_selector_all("td")
                if len(cells) >= 2:
                    emp_no = cells[0].inner_text().strip()
                    punch_datetime_str = cells[1].inner_text().strip()

                    # 檢查是否是今天的記錄
                    if target_date in punch_datetime_str:
                        today_records.append({
                            'emp_no': emp_no,
                            'datetime': punch_datetime_str
                        })
            except:
                continue

        if today_records:
            print(f"找到今天 {target_date} 的打卡記錄：")
            for record in today_records:
                print(f"  員工編號：{record['emp_no']}, 打卡時間：{record['datetime']}")

            # 檢查最新記錄是否在打卡時間附近（前後10分鐘內）
            latest_record = today_records[0]  # 第一筆通常是最新的
            latest_datetime_str = latest_record['datetime']

            try:
                # 解析最新打卡時間
                latest_datetime = datetime.datetime.strptime(latest_datetime_str, "%Y-%m-%d %H:%M")

                # 檢查是否在同一分鐘內
                punch_minute = punch_time.strftime("%Y-%m-%d %H:%M")
                latest_minute = latest_datetime.strftime("%Y-%m-%d %H:%M")

                print(f"最新打卡時間：{latest_datetime_str}")
                print(f"執行打卡時間：{punch_minute}")

                if punch_minute == latest_minute:
                    print("✅ 打卡成功！記錄時間完全匹配（同一分鐘）。")
                    return True
                else:
                    time_diff = abs((latest_datetime - punch_time).total_seconds() / 60)  # 轉換為分鐘
                    print(f"時間差：{time_diff:.1f} 分鐘")
                    print("❌ 打卡記錄時間與執行時間不在同一分鐘內，打卡可能失敗。")
                    return False

            except ValueError as e:
                print(f"時間解析錯誤：{e}")
                print("❌ 無法驗證打卡時間。")
                return False
        else:
            print(f"❌ 今天 {target_date} 沒有找到打卡記錄。")
            return False

    except Exception as e:
        print(f"查詢打卡記錄時發生錯誤：{e}")
        return False

def main():
    try:
        with sync_playwright() as p:
            # 啟動瀏覽器 (使用 headless 模式避免衝突)
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()

            # 訪問登入頁面
            page.goto("https://openerp.entrust.com.tw/pcpunch/Login.aspx")

            # 等待頁面加載完成
            page.wait_for_timeout(3000)  # 等待3秒讓頁面加載

            # 填寫帳號和密碼
            # 增加等待時間確保元素可用
            page.wait_for_selector("input[name='txt_EmpNo']", timeout=60000)
            page.fill("input[name='txt_EmpNo']", username)
            page.wait_for_selector("input[name='txt_Password']", timeout=60000)
            page.fill("input[name='txt_Password']", password)

            # 點擊登入按鈕
            page.wait_for_selector("input[id='btnLogin']", timeout=60000)
            page.click("input[id='btnLogin']")

            # 等待頁面加載
            # 等待頁面加載完成（替換為正確的 wait_for_load_state 方法）
            page.wait_for_load_state('networkidle', timeout=60000)

            # 檢查是否登入成功
            try:
                # 等待打卡相關元素出現，如果60秒內出現則表示登入成功
                page.wait_for_selector("text=打卡", timeout=60000)
                print("登入成功！")

                # 記錄打卡前的時間
                punch_time = datetime.datetime.now()
                print(f"準備打卡，當前時間：{punch_time.strftime('%Y-%m-%d %H:%M')}")

                # 執行打卡動作
                punch_success = False
                try:
                    # 設置對話框處理器
                    dialog_message = None
                    def handle_dialog(dialog):
                        nonlocal dialog_message
                        dialog_message = dialog.message
                        dialog.accept()

                    page.on("dialog", handle_dialog)

                    # 點擊打卡按鈕 (使用更精確的選擇器)
                    try:
                        page.click("input[value='打卡']", timeout=5000)
                        print("已點擊打卡按鈕 (input)")
                    except:
                        try:
                            page.click("button:has-text('打卡')", timeout=5000)
                            print("已點擊打卡按鈕 (button)")
                        except:
                            # 嘗試使用更通用的選擇器
                            page.click("[value='打卡'], button:contains('打卡')", timeout=5000)
                            print("已點擊打卡按鈕 (通用)")

                    # 等待一下讓對話框出現
                    page.wait_for_timeout(3000)

                    # 檢查是否收到打卡成功的對話框
                    if dialog_message and "打卡成功" in dialog_message:
                        print("✅ 看到打卡成功提示！")
                        punch_success = True
                    else:
                        print(f"⚠️ 對話框訊息：{dialog_message}")
                        print("⚠️ 未看到打卡成功提示，可能已經打過卡或發生其他問題")

                except Exception as e:
                    print(f"打卡過程中發生錯誤：{e}")

                if not punch_success:
                    print("❌ 打卡失敗，但仍會檢查記錄")
                    return False

                # 導航到打卡記錄查詢頁面
                page.goto("https://openerp.entrust.com.tw/pcpunch/EmpSignInQuery.aspx", timeout=60000)
                print("正在查詢打卡記錄...")

                # 等待頁面加載
                page.wait_for_timeout(2000)

                # 查詢今天的打卡記錄
                return check_punch_records(page, today, punch_time)

            except Exception as e:
                print(f"登入失敗：{e}")
                return False  # 登入失敗

    except Exception as e:
        print(f"執行過程中發生錯誤: {e}")
        return False

# 執行主函數，最多重試5次
success = False
for attempt in range(5):
    print(f"嘗試 {attempt+1}/5...")
    success = main()
    if success:
        print("打卡檢查完成！")
        break
    else:
        if attempt < 4:  # 不是最後一次嘗試
            print("等待5秒後重試...")
            time.sleep(5)  # 等待5秒後重試

if not success:
    print("所有嘗試都失敗了。")